import React, { useState, useReducer, useCallback, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { ArrowLeft } from 'lucide-react'

// Enhanced Template Editor Components
import { TemplateEditorCanvas } from '../components/TemplateEditor/TemplateEditorCanvas'
import { ComponentLibrary } from '../components/TemplateEditor/ComponentLibrary'
import { PropertiesPanel } from '../components/TemplateEditor/PropertiesPanel'
import { TemplateEditorToolbar } from '../components/TemplateEditor/TemplateEditorToolbar'

// Types
import { TemplateComponent, TemplateEditorState, TemplateEditorAction } from '../types/templateEditor'

// API
import { templateEditorApi, TemplateData } from '../services/templateEditorApi'

// Components
import { PreviewModal } from '../components/TemplateEditor/PreviewModal'
import axios from 'axios'

// Template Editor Reducer
const templateEditorReducer = (state: TemplateEditorState, action: TemplateEditorAction): TemplateEditorState => {
  switch (action.type) {
    case 'ADD_COMPONENT':
      const newComponents = [...state.components, action.payload]
      return {
        ...state,
        components: newComponents,
        selectedComponent: action.payload.id,
        history: [...state.history.slice(0, state.historyIndex + 1), newComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }

    case 'UPDATE_COMPONENT':
      const updatedComponents = state.components.map(comp =>
        comp.id === action.payload.id ? { ...comp, ...action.payload.updates } : comp
      )
      return {
        ...state,
        components: updatedComponents,
        history: [...state.history.slice(0, state.historyIndex + 1), updatedComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }

    case 'DELETE_COMPONENT':
      const filteredComponents = state.components.filter(comp => comp.id !== action.payload)
      return {
        ...state,
        components: filteredComponents,
        selectedComponent: state.selectedComponent === action.payload ? null : state.selectedComponent,
        history: [...state.history.slice(0, state.historyIndex + 1), filteredComponents],
        historyIndex: state.historyIndex + 1,
        isDirty: true
      }

    case 'SELECT_COMPONENT':
      return {
        ...state,
        selectedComponent: action.payload
      }

    case 'SET_ZOOM':
      return {
        ...state,
        zoom: action.payload
      }

    case 'TOGGLE_GRID':
      return {
        ...state,
        showGrid: !state.showGrid
      }

    case 'SET_PREVIEW_MODE':
      return {
        ...state,
        previewMode: action.payload
      }

    case 'UNDO':
      if (state.historyIndex > 0) {
        return {
          ...state,
          components: state.history[state.historyIndex - 1],
          historyIndex: state.historyIndex - 1,
          isDirty: true
        }
      }
      return state

    case 'REDO':
      if (state.historyIndex < state.history.length - 1) {
        return {
          ...state,
          components: state.history[state.historyIndex + 1],
          historyIndex: state.historyIndex + 1,
          isDirty: true
        }
      }
      return state

    case 'SAVE_STATE':
      return {
        ...state,
        isDirty: false
      }

    default:
      return state
  }
}

const TemplateEditor: React.FC = () => {
  const navigate = useNavigate()
  const { id } = useParams()
  const isEditing = Boolean(id)

  // Template metadata
  const [templateName, setTemplateName] = useState('')
  const [templateDescription, setTemplateDescription] = useState('')
  const [viewMode, setViewMode] = useState<'visual' | 'code'>('visual')
  const [htmlContent, setHtmlContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  // Add loading overlay when isLoading is true
  useEffect(() => {
    if (isLoading) {
      document.body.classList.add('cursor-wait')
    } else {
      document.body.classList.remove('cursor-wait')
    }
    return () => {
      document.body.classList.remove('cursor-wait')
    }
  }, [isLoading])

  // Template editor state
  const [state, dispatch] = useReducer(templateEditorReducer, {
    components: [],
    selectedComponent: null,
    history: [[]],
    historyIndex: 0,
    zoom: 1,
    showGrid: true,
    previewMode: 'desktop',
    isDirty: false
  })

  // Load template data if editing
  useEffect(() => {
    const loadTemplate = async () => {
      if (isEditing && id) {
        try {
          setIsLoading(true)
          const template = await templateEditorApi.getTemplate(id)

          setTemplateName(template.name)
          setTemplateDescription(template.description || '')
          setHtmlContent(template.html_content)

          if (template.components && Array.isArray(template.components)) {
            // Load each component individually to maintain state structure
            template.components.forEach((component: TemplateComponent) => {
              dispatch({
                type: 'ADD_COMPONENT',
                payload: component
              })
            })
          }
        } catch (error) {
          if (axios.isAxiosError(error) && error.response?.status === 404) {
            // Handle 404 specifically
            alert(`Template not found. It may have been deleted or you don't have permission to access it.`)
            navigate('/templates') // Redirect to templates list
          } else {
            // Handle other errors
            console.error('Failed to load template:', error)
            alert('An error occurred while loading the template. Please try again later.')
          }
        } finally {
          setIsLoading(false)
        }
      }
    }

    loadTemplate()
  }, [isEditing, id, navigate])

  // Generate HTML from components
  const generateHTML = useCallback(() => {
    if (viewMode === 'code') {
      return htmlContent
    }
    return templateEditorApi.generateHTML(state.components)
  }, [state.components, htmlContent, viewMode])

  // Auto-save functionality
  useEffect(() => {
    if (!state.isDirty || !templateName.trim()) return

    const autoSaveTimer = setTimeout(async () => {
      try {
        const templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'> = {
          name: templateName,
          description: templateDescription,
          html_content: generateHTML(),
          components: state.components,
          settings: {
            width: 600,
            backgroundColor: '#ffffff',
            fontFamily: 'Arial, sans-serif'
          }
        }

        if (isEditing && id) {
          await templateEditorApi.updateTemplate(id, templateData)
          dispatch({ type: 'SAVE_STATE' })
          console.log('Template auto-saved')
        }
      } catch (error) {
        console.error('Auto-save failed:', error)
      }
    }, 2000) // Auto-save after 2 seconds of inactivity

    return () => clearTimeout(autoSaveTimer)
  }, [state.isDirty, state.components, templateName, templateDescription, generateHTML, isEditing, id])

  // Event handlers
  const handleComponentAdd = useCallback((component: TemplateComponent) => {
    dispatch({ type: 'ADD_COMPONENT', payload: component })
  }, [])

  const handleComponentUpdate = useCallback((id: string, updates: Partial<TemplateComponent>) => {
    dispatch({ type: 'UPDATE_COMPONENT', payload: { id, updates } })
  }, [])

  const handleComponentDelete = useCallback((id: string) => {
    dispatch({ type: 'DELETE_COMPONENT', payload: id })
  }, [])

  const handleComponentSelect = useCallback((id: string | null) => {
    dispatch({ type: 'SELECT_COMPONENT', payload: id })
  }, [])

  const handleZoomChange = useCallback((zoom: number) => {
    dispatch({ type: 'SET_ZOOM', payload: zoom })
  }, [])

  const handleToggleGrid = useCallback(() => {
    dispatch({ type: 'TOGGLE_GRID' })
  }, [])

  const handlePreviewModeChange = useCallback((mode: 'desktop' | 'tablet' | 'mobile') => {
    dispatch({ type: 'SET_PREVIEW_MODE', payload: mode })
  }, [])

  const handleUndo = useCallback(() => {
    dispatch({ type: 'UNDO' })
  }, [])

  const handleRedo = useCallback(() => {
    dispatch({ type: 'REDO' })
  }, [])

  const handleSave = async () => {
    try {
      setIsLoading(true)

      const templateData: Omit<TemplateData, 'id' | 'created_at' | 'updated_at'> = {
        name: templateName,
        description: templateDescription,
        html_content: generateHTML(),
        components: state.components,
        settings: {
          width: 600,
          backgroundColor: '#ffffff',
          fontFamily: 'Arial, sans-serif'
        }
      }

      if (isEditing && id) {
        await templateEditorApi.updateTemplate(id, templateData)
      } else {
        await templateEditorApi.createTemplate(templateData)
      }

      dispatch({ type: 'SAVE_STATE' })
      navigate('/templates')
    } catch (error) {
      console.error('Failed to save template:', error)
      // TODO: Show error notification
    } finally {
      setIsLoading(false)
    }
  }

  const handlePreview = () => {
    setShowPreview(true)
  }

  const handleExport = () => {
    const templateData: TemplateData = {
      name: templateName,
      description: templateDescription,
      html_content: generateHTML(),
      components: state.components
    }
    templateEditorApi.exportTemplate(templateData, 'html')
  }

  const handleImport = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.html'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const html = e.target?.result as string
          setHtmlContent(html)
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  const handleSendTest = async () => {
    const email = prompt('Enter email address for test:')
    if (email) {
      try {
        const templateData: TemplateData = {
          name: templateName,
          description: templateDescription,
          html_content: generateHTML(),
          components: state.components
        }
        await templateEditorApi.sendTestEmail(templateData, email)
        // TODO: Show success notification
      } catch (error) {
        console.error('Failed to send test email:', error)
        // TODO: Show error notification
      }
    }
  }

  const selectedComponentData = state.selectedComponent
    ? state.components.find(c => c.id === state.selectedComponent) || null
    : null

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col">
        {/* Back Button */}
        <div className="bg-white border-b px-6 py-2">
          <button
            onClick={() => navigate('/templates')}
            className="flex items-center text-gray-600 hover:text-gray-900"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Templates
          </button>
        </div>

        {/* Toolbar */}
        <TemplateEditorToolbar
          templateName={templateName}
          onTemplateNameChange={setTemplateName}
          viewMode={viewMode}
          onViewModeChange={setViewMode}
          previewMode={state.previewMode}
          onPreviewModeChange={handlePreviewModeChange}
          showGrid={state.showGrid}
          onToggleGrid={handleToggleGrid}
          canUndo={state.historyIndex > 0}
          canRedo={state.historyIndex < state.history.length - 1}
          onUndo={handleUndo}
          onRedo={handleRedo}
          onSave={handleSave}
          onPreview={handlePreview}
          onExport={handleExport}
          onImport={handleImport}
          onSendTest={handleSendTest}
          isDirty={state.isDirty}
        />

        {/* Template Settings */}
        <div className="bg-white border-b px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description
              </label>
              <input
                type="text"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                className="input"
                placeholder="Enter template description"
              />
            </div>
          </div>
        </div>

        {/* Editor Content */}
        <div className="flex-1 flex overflow-hidden">
          {viewMode === 'visual' ? (
            <>
              {/* Component Library */}
              <ComponentLibrary onComponentAdd={handleComponentAdd} />

              {/* Canvas */}
              <TemplateEditorCanvas
                components={state.components}
                selectedComponent={state.selectedComponent}
                onComponentSelect={handleComponentSelect}
                onComponentUpdate={handleComponentUpdate}
                onComponentAdd={handleComponentAdd}
                onComponentDelete={handleComponentDelete}
                zoom={state.zoom}
                onZoomChange={handleZoomChange}
                showGrid={state.showGrid}
                previewMode={state.previewMode}
              />

              {/* Properties Panel */}
              <PropertiesPanel
                selectedComponent={selectedComponentData}
                onComponentUpdate={(updates) => {
                  if (state.selectedComponent) {
                    handleComponentUpdate(state.selectedComponent, updates)
                  }
                }}
                previewMode={state.previewMode}
                onPreviewModeChange={handlePreviewModeChange}
              />
            </>
          ) : (
            /* Code Editor */
            <div className="flex-1 p-6">
              <div className="h-full">
                <textarea
                  value={htmlContent}
                  onChange={(e) => setHtmlContent(e.target.value)}
                  className="w-full h-full p-4 border border-gray-300 rounded-lg font-mono text-sm resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your HTML content here..."
                />
              </div>
            </div>
          )}
        </div>

        {/* Preview Modal */}
        <PreviewModal
          isOpen={showPreview}
          onClose={() => setShowPreview(false)}
          htmlContent={generateHTML()}
          templateName={templateName}
        />
      </div>
    </DndProvider>
  )
}

export default TemplateEditor


