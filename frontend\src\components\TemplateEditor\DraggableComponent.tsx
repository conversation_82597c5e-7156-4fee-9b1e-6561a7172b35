import React, { useRef } from 'react'
import { useDrag } from 'react-dnd'
import { TemplateComponent } from '../../types/templateEditor'
import { ComponentRenderer } from './ComponentRenderer'

interface DraggableComponentProps {
  component: TemplateComponent
  isSelected: boolean
  onSelect: () => void
  onUpdate: (updates: Partial<TemplateComponent>) => void
  zoom: number
}

export const DraggableComponent: React.FC<DraggableComponentProps> = ({
  component,
  isSelected,
  onSelect,
  onUpdate,
  zoom
}) => {
  const ref = useRef<HTMLDivElement>(null)

  const [{ isDragging }, drag] = useDrag({
    type: 'existing-component',
    item: {
      type: 'existing-component',
      id: component.id,
      styles: component.styles
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  })

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect()
  }

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    // Enable inline editing for text components
    if (component.type === 'text' || component.type === 'heading') {
      const element = e.target as HTMLElement
      if (element.contentEditable !== 'true') {
        element.contentEditable = 'true'
        element.focus()
        
        const handleBlur = () => {
          element.contentEditable = 'false'
          onUpdate({
            content: {
              ...component.content,
              text: element.textContent || ''
            }
          })
          element.removeEventListener('blur', handleBlur)
        }
        
        element.addEventListener('blur', handleBlur)
      }
    }
  }

  drag(ref)

  return (
    <div
      ref={ref}
      className={`absolute cursor-pointer transition-all duration-200 ${
        isSelected ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
      } ${isDragging ? 'opacity-50' : ''}`}
      style={{
        left: component.position.x,
        top: component.position.y,
        zIndex: component.styles?.zIndex || 1,
        transform: `scale(${zoom})`,
        transformOrigin: 'top left'
      }}
      onMouseDown={handleMouseDown}
      onDoubleClick={handleDoubleClick}
    >
      <ComponentRenderer 
        component={component} 
        isSelected={isSelected}
        onUpdate={onUpdate}
      />
      
      {isSelected && (
        <div className="absolute inset-0 pointer-events-none">
          {/* Selection handles */}
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full"></div>
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
          <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-blue-500 rounded-full"></div>
          <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
        </div>
      )}
    </div>
  )
}
